<?php

namespace Database\Factories;

use App\Models\Hrm\Attendance\DutySchedule;
use Illuminate\Database\Eloquent\Factories\Factory;

class DutyScheduleFactory extends Factory
{
    protected $model = DutySchedule::class;

    public function definition(): array
    {
        $shifts = [
            [
                'shift' => 'Morning Shift',
                'start_time' => '09:00:00',
                'end_time' => '17:00:00',
                'hour' => 8,
                'max_over_time' => 4,
            ],
            [
                'shift' => 'Day Shift',
                'start_time' => '10:00:00',
                'end_time' => '18:00:00',
                'hour' => 8,
                'max_over_time' => 3,
            ],
            [
                'shift' => 'Evening Shift',
                'start_time' => '15:00:00',
                'end_time' => '23:00:00',
                'hour' => 8,
                'max_over_time' => 2,
            ],
            [
                'shift' => 'Night Shift',
                'start_time' => '23:00:00',
                'end_time' => '07:00:00',
                'hour' => 8,
                'max_over_time' => 2,
            ],
            [
                'shift' => 'Flexible Hours',
                'start_time' => '08:00:00',
                'end_time' => '16:00:00',
                'hour' => 8,
                'max_over_time' => 6,
            ],
        ];

        $selectedShift = $this->faker->randomElement($shifts);

        return [
            'shift' => $selectedShift['shift'],
            'start_time' => $selectedShift['start_time'],
            'end_time' => $selectedShift['end_time'],
            'consider_time' => $this->faker->numberBetween(10, 30),
            'hour' => $selectedShift['hour'],
            'max_over_time' => $selectedShift['max_over_time'],
            'status_id' => 1, // Active
            'end_on_same_date' => $selectedShift['shift'] === 'Night Shift' ? 0 : 1,
            'company_id' => 1,
            'branch_id' => 1,
        ];
    }

    /**
     * Create a morning shift
     */
    public function morningShift(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'shift' => 'Morning Shift',
                'start_time' => '09:00:00',
                'end_time' => '17:00:00',
                'hour' => 8,
                'max_over_time' => 4,
                'end_on_same_date' => 1,
            ];
        });
    }

    /**
     * Create a night shift
     */
    public function nightShift(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'shift' => 'Night Shift',
                'start_time' => '23:00:00',
                'end_time' => '07:00:00',
                'hour' => 8,
                'max_over_time' => 2,
                'end_on_same_date' => 0,
            ];
        });
    }

    /**
     * Create a part-time shift
     */
    public function partTime(): static
    {
        return $this->state(function (array $attributes) {
            $partTimeShifts = [
                [
                    'shift' => 'Part Time Morning',
                    'start_time' => '09:00:00',
                    'end_time' => '13:00:00',
                    'hour' => 4,
                ],
                [
                    'shift' => 'Part Time Evening',
                    'start_time' => '14:00:00',
                    'end_time' => '18:00:00',
                    'hour' => 4,
                ],
            ];

            $selectedShift = $this->faker->randomElement($partTimeShifts);

            return [
                'shift' => $selectedShift['shift'],
                'start_time' => $selectedShift['start_time'],
                'end_time' => $selectedShift['end_time'],
                'hour' => $selectedShift['hour'],
                'max_over_time' => 2,
                'end_on_same_date' => 1,
            ];
        });
    }

    /**
     * Create a flexible hours shift
     */
    public function flexible(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'shift' => 'Flexible Hours',
                'start_time' => '08:00:00',
                'end_time' => '16:00:00',
                'consider_time' => 30,
                'hour' => 8,
                'max_over_time' => 6,
                'end_on_same_date' => 1,
            ];
        });
    }
}
