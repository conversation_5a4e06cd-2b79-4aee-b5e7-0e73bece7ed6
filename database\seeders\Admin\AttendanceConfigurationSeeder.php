<?php

namespace Database\Seeders\Admin;

use App\Models\AttendanceConfiguration;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;

class AttendanceConfigurationSeeder extends Seeder
{
    public function run()
    {
        Model::unguard();

        // Clear existing configurations
        AttendanceConfiguration::truncate();

        $attendanceConfigurations = [];
        $users                    = User::with('role')->get();

        foreach ($users as $user) {
            $config                     = $this->generateConfigForUser($user);
            $attendanceConfigurations[] = $config;
        }

        AttendanceConfiguration::insert($attendanceConfigurations);

        $this->command->info('Created attendance configurations for ' . count($attendanceConfigurations) . ' users.');

        Model::reguard();
    }

    /**
     * Generate attendance configuration based on user role and characteristics
     */
    private function generateConfigForUser(User $user): array
    {
        $roleSlug = $user->role->slug ?? 'staff';

        // Different attendance methods based on role
        $attendanceMethods = $this->getAttendanceMethodsForRole($roleSlug);

        // Different weekend configurations
        $weekends = $this->getWeekendsForUser($user);

        // IP and location restrictions based on role
        $ipRestrictions       = $this->getIPRestrictionsForRole($roleSlug);
        $locationRestrictions = $this->getLocationRestrictionsForRole($roleSlug);

        return [
            'user_id'           => $user->id,
            'time_zone'         => $this->getTimezoneForUser($user),
            'attendance_method' => json_encode($attendanceMethods),
            'weekends'          => json_encode($weekends),
            'is_free_ip'        => $ipRestrictions,
            'is_free_location'  => $locationRestrictions,
            'created_at'        => now(),
            'updated_at'        => now(),
        ];
    }

    /**
     * Get attendance methods based on role
     */
    private function getAttendanceMethodsForRole(string $roleSlug): array
    {
        $methodsByRole = [
            'superadmin' => ['normal_attendance', 'face_recognition', 'qr_code'],
            'admin'      => ['normal_attendance', 'face_recognition'],
            'hr'         => ['normal_attendance', 'face_recognition'],
            'staff'      => ['normal_attendance'],
        ];

        return $methodsByRole[$roleSlug] ?? ['normal_attendance'];
    }

    /**
     * Get weekends configuration for user
     */
    private function getWeekendsForUser(User $user): array
    {
        // Most users have Saturday-Sunday weekends
        $weekendOptions = [
            ['Saturday', 'Sunday'], // 80% probability
            ['Friday', 'Saturday'], // 15% probability
            ['Sunday'], // 5% probability (single day weekend)
        ];

        $rand = rand(1, 100);
        if ($rand <= 80) {
            return $weekendOptions[0];
        } elseif ($rand <= 95) {
            return $weekendOptions[1];
        } else {
            return $weekendOptions[2];
        }
    }

    /**
     * Get IP restrictions based on role
     */
    private function getIPRestrictionsForRole(string $roleSlug): bool
    {
        // Higher level roles have more freedom
        $freeIPByRole = [
            'superadmin' => true,
            'admin'      => true,
            'hr'         => rand(1, 100) <= 80, // 80% chance of free IP
            'staff'      => rand(1, 100) <= 60, // 60% chance of free IP
        ];

        return $freeIPByRole[$roleSlug] ?? false;
    }

    /**
     * Get location restrictions based on role
     */
    private function getLocationRestrictionsForRole(string $roleSlug): bool
    {
        // Higher level roles have more freedom
        $freeLocationByRole = [
            'superadmin' => true,
            'admin'      => true,
            'hr'         => rand(1, 100) <= 70, // 70% chance of free location
            'staff'      => rand(1, 100) <= 50, // 50% chance of free location
        ];

        return $freeLocationByRole[$roleSlug] ?? false;
    }

    /**
     * Get timezone for user (could be based on location/branch in future)
     */
    private function getTimezoneForUser(User $user): string
    {
        // For now, most users are in Asia/Dhaka timezone
        // In future, this could be based on branch location
        $timezones = [
            'Asia/Dhaka'   => 90, // 90% probability
            'Asia/Kolkata' => 5, // 5% probability
            'UTC'          => 5, // 5% probability
        ];

        $rand = rand(1, 100);
        if ($rand <= 90) {
            return 'Asia/Dhaka';
        } elseif ($rand <= 95) {
            return 'Asia/Kolkata';
        } else {
            return 'UTC';
        }
    }
}
