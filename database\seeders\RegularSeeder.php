<?php

namespace Database\Seeders;

use Database\Seeders\Admin\AttendanceConfigurationSeeder;
use Database\Seeders\Admin\CompanySeeder;
use Database\Seeders\Admin\PermissionSeeder;
use Database\Seeders\Admin\RoleSeeder;
use Database\Seeders\Admin\SalaryConfigurationSeeder;
use Database\Seeders\Admin\StatusSeeder;
use Database\Seeders\Admin\SubscriptionSeeder;
use Database\Seeders\Admin\UserDutyScheduleSeeder;
use Database\Seeders\Admin\UserInfoSeeder;
use Database\Seeders\Admin\UserSeeder;
use Database\Seeders\Admin\UserWisePermissionSeeder;
use Database\Seeders\Hrm\AppSetting\AppScreenSeeder;
use Database\Seeders\Hrm\Country\CountrySeeder;
use Database\Seeders\Hrm\Leave\LeaveAssignSeeder;
use Database\Seeders\Hrm\Leave\LeaveRequestSeeder;
use Database\Seeders\Hrm\Leave\LeaveTypeSeeder;
use Database\Seeders\Traits\ApplicationKeyGenerate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class RegularSeeder extends Seeder
{
    use ApplicationKeyGenerate;

    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // Disable foreign key constraints
        Schema::disableForeignKeyConstraints();

        // Disable mass assignment protection
        Model::unguard();

        // First run critical seeders that create reference tables
        $this->runCriticalSeeders();

        // Then run the rest of the seeders
        $this->runMainSeeders();

        // Enable foreign key constraints
        Schema::enableForeignKeyConstraints();

        // Re-enable mass assignment protection
        Model::reguard();
    }

    /**
     * Run critical seeders that establish reference tables
     * These must run first because other tables depend on them
     */
    protected function runCriticalSeeders()
    {
        // First populate status tables
        if (Schema::hasTable('statuses')) {
            $this->call(StatusSeeder::class);
        }

        // Next populate currencies
        if (Schema::hasTable('currencies')) {
            $this->call(CurrencySeeder::class);
        }

        // Next populate countries
        if (Schema::hasTable('countries')) {
            $this->call(CountrySeeder::class);
        }

        // Next populate company and branch tables
        $this->call(CompanySeeder::class);
        $this->call(BranchSeeder::class);

        // Now that we have companies and branches, populate languages
        if (Schema::hasTable('languages')) {
            $this->call(LanguageSeeder::class);
        }
    }

    /**
     * Run the main seeders that depend on reference tables
     */
    protected function runMainSeeders()
    {
        // Company and branch are now in critical seeders
        $this->call(RoleSeeder::class);
        $this->call(PermissionSeeder::class);
        $this->call(DesignationSeeder::class);
        $this->call(DepartmentSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(SettingsSeeder::class);
        $this->call(UploadSeeder::class);
        $this->call(CompanyConfigSeeder::class);
        $this->call(SubscriptionSeeder::class);
        $this->call(DutyScheduleSeeder::class);
        $this->call(WeekendSetupSeeder::class);
        $this->call(AllContentsTableSeeder::class);
        $this->call(AppScreenSeeder::class);
        $this->call(BrandingSeeder::class);
        $this->call(LeaveTypeSeeder::class);
        $this->call(LeaveAssignSeeder::class);
        $this->call(LeaveRequestSeeder::class);
        $this->call(UserInfoSeeder::class);
        $this->call(UserWisePermissionSeeder::class);
        $this->call(AttendanceConfigurationSeeder::class);
        $this->call(UserDutyScheduleSeeder::class);
        $this->call(AttendanceSeeder::class);
        $this->call(SalaryConfigurationSeeder::class);
        $this->call(ExpenseCategorySeeder::class);
        $this->call(StickyNoteSeeder::class);
        $this->call(ModuleSeeder::class);
    }
}
