<?php

namespace Database\Factories;

use App\Models\AttendanceConfiguration;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttendanceConfigurationFactory extends Factory
{
    protected $model = AttendanceConfiguration::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'time_zone' => $this->faker->randomElement([
                'Asia/Dhaka',
                'Asia/Kolkata', 
                'UTC',
                'Asia/Dubai',
                'Europe/London'
            ]),
            'attendance_method' => $this->faker->randomElement([
                ['normal_attendance'],
                ['normal_attendance', 'face_recognition'],
                ['normal_attendance', 'qr_code'],
                ['normal_attendance', 'face_recognition', 'qr_code'],
            ]),
            'weekends' => $this->faker->randomElement([
                ['Saturday', 'Sunday'],
                ['Friday', 'Saturday'],
                ['Sunday'],
                ['Friday'],
            ]),
            'is_free_ip' => $this->faker->boolean(70), // 70% chance of free IP
            'is_free_location' => $this->faker->boolean(60), // 60% chance of free location
        ];
    }

    /**
     * Configuration for admin users
     */
    public function admin(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'attendance_method' => ['normal_attendance', 'face_recognition', 'qr_code'],
                'is_free_ip' => true,
                'is_free_location' => true,
                'time_zone' => 'Asia/Dhaka',
            ];
        });
    }

    /**
     * Configuration for staff users
     */
    public function staff(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'attendance_method' => ['normal_attendance'],
                'is_free_ip' => $this->faker->boolean(50),
                'is_free_location' => $this->faker->boolean(40),
            ];
        });
    }

    /**
     * Configuration with strict restrictions
     */
    public function restricted(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'attendance_method' => ['normal_attendance'],
                'is_free_ip' => false,
                'is_free_location' => false,
                'weekends' => ['Saturday', 'Sunday'],
            ];
        });
    }

    /**
     * Configuration with flexible settings
     */
    public function flexible(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'attendance_method' => ['normal_attendance', 'face_recognition', 'qr_code'],
                'is_free_ip' => true,
                'is_free_location' => true,
                'weekends' => $this->faker->randomElement([
                    ['Saturday', 'Sunday'],
                    ['Friday', 'Saturday'],
                    ['Sunday'],
                ]),
            ];
        });
    }

    /**
     * Configuration for remote workers
     */
    public function remote(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'attendance_method' => ['normal_attendance'],
                'is_free_ip' => true,
                'is_free_location' => true,
                'time_zone' => $this->faker->randomElement([
                    'Asia/Dhaka',
                    'UTC',
                    'Asia/Kolkata',
                    'Europe/London',
                    'America/New_York'
                ]),
            ];
        });
    }

    /**
     * Configuration for part-time workers
     */
    public function partTime(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'attendance_method' => ['normal_attendance'],
                'weekends' => $this->faker->randomElement([
                    ['Saturday', 'Sunday', 'Monday'],
                    ['Friday', 'Saturday', 'Sunday'],
                    ['Sunday', 'Monday'],
                ]),
                'is_free_ip' => $this->faker->boolean(60),
                'is_free_location' => $this->faker->boolean(50),
            ];
        });
    }
}
