<?php

namespace Database\Seeders\Admin;

use App\Models\Hrm\Attendance\DutySchedule;
use App\Models\Hrm\Attendance\UserDutySchedule;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;

class UserDutyScheduleSeeder extends Seeder
{
    public function run()
    {
        Model::unguard();

        // Clear existing user duty schedule assignments
        UserDutySchedule::truncate();

        $users         = User::with('role')->get();
        $dutySchedules = DutySchedule::all();

        if ($users->isEmpty() || $dutySchedules->isEmpty()) {
            $this->command->warn('No users or duty schedules found. Please run UserSeeder and DutyScheduleSeeder first.');
            return;
        }

        $userDutySchedules = [];

        foreach ($users as $user) {
            // Assign duty schedules based on role and user characteristics
            $assignedScheduleId = $this->getDutyScheduleForUser($user, $dutySchedules);

            $userDutySchedules[] = [
                'user_id'          => $user->id,
                'duty_schedule_id' => $assignedScheduleId,
                'created_at'       => now(),
                'updated_at'       => now(),
            ];
        }

        UserDutySchedule::insert($userDutySchedules);

        $this->command->info('Assigned duty schedules to ' . count($userDutySchedules) . ' users.');

        Model::reguard();
    }

    /**
     * Determine the appropriate duty schedule for a user based on their role and other factors
     */
    private function getDutyScheduleForUser(User $user, $dutySchedules): int
    {
        $roleSlug = $user->role->slug ?? 'staff';

        // Define role-based schedule preferences
        $roleScheduleMap = [
            'superadmin' => ['Morning Shift', 'Flexible Hours'],
            'admin'      => ['Morning Shift', 'Day Shift', 'Flexible Hours'],
            'hr'         => ['Morning Shift', 'Day Shift'],
            'staff'      => ['Morning Shift', 'Day Shift', 'Evening Shift', 'Part Time Morning', 'Part Time Evening'],
        ];

        // Get preferred schedules for this role
        $preferredSchedules = $roleScheduleMap[$roleSlug] ?? ['Morning Shift', 'Day Shift'];

        // Filter duty schedules to match preferred ones
        $availableSchedules = $dutySchedules->whereIn('shift', $preferredSchedules);

        // If no preferred schedules found, fall back to all schedules
        if ($availableSchedules->isEmpty()) {
            $availableSchedules = $dutySchedules;
        }

        // For part-time employees, prefer part-time schedules
        if ($user->job_type === 'part_time') {
            $partTimeSchedules = $dutySchedules->filter(function ($schedule) {
                return str_contains($schedule->shift, 'Part Time');
            });

            if ($partTimeSchedules->isNotEmpty()) {
                $availableSchedules = $partTimeSchedules;
            }
        }

        // Randomly select from available schedules to add variety
        $selectedSchedule = $availableSchedules->random();

        return $selectedSchedule->id;
    }
}
