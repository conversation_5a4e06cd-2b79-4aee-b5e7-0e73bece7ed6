<?php

namespace Database\Seeders;

use App\Enums\AttendanceStatus;
use App\Models\Hrm\Attendance\Attendance;
use App\Models\Hrm\Attendance\DutySchedule;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AttendanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $this->command->info('Creating attendance records...');

        // Get all users with their duty schedules
        $users         = User::with(['dutySchedules', 'attendanceConfig'])->get();
        $dutySchedules = DutySchedule::all();

        if ($users->isEmpty() || $dutySchedules->isEmpty()) {
            $this->command->warn('No users or duty schedules found. Please run UserSeeder and DutyScheduleSeeder first.');
            return;
        }

        // Generate attendance for the last 30 days
        $startDate = Carbon::now()->subDays(30);
        $endDate   = Carbon::now()->subDay(); // Exclude today

        $attendanceRecords = [];

        foreach ($users as $user) {
            $this->command->info("Creating attendance for user: {$user->name}");

            // Get user's duty schedule (default to first one if none assigned)
            $userDutySchedule = $user->dutySchedules->first() ?? $dutySchedules->first();

            $currentDate = $startDate->copy();

            while ($currentDate->lte($endDate)) {
                // Skip weekends (Saturday and Sunday by default)
                if (!$this->isWeekend($currentDate, $user)) {
                    $attendanceRecord = $this->generateAttendanceRecord($user, $currentDate, $userDutySchedule);
                    if ($attendanceRecord) {
                        $attendanceRecords[] = $attendanceRecord;
                    }
                }

                $currentDate->addDay();
            }
        }

        // Insert all records in chunks for better performance
        if (!empty($attendanceRecords)) {
            $chunks = array_chunk($attendanceRecords, 100);
            foreach ($chunks as $chunk) {
                Attendance::insert($chunk);
            }

            $this->command->info('Created ' . count($attendanceRecords) . ' attendance records.');
        }

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    /**
     * Check if the given date is a weekend for the user
     */
    private function isWeekend(Carbon $date, User $user): bool
    {
        $weekends = $user->attendanceConfig->weekends ?? ['Saturday', 'Sunday'];
        return in_array($date->format('l'), $weekends);
    }

    /**
     * Generate a single attendance record for a user on a specific date
     */
    private function generateAttendanceRecord(User $user, Carbon $date, DutySchedule $dutySchedule): ?array
    {
        // 10% chance of being absent
        if (rand(1, 100) <= 10) {
            return $this->generateAbsentRecord($user, $date, $dutySchedule);
        }

        // 20% chance of being late
        $isLate = rand(1, 100) <= 20;

        // 15% chance of leaving early
        $isEarlyExit = rand(1, 100) <= 15;

        return $this->generatePresentRecord($user, $date, $dutySchedule, $isLate, $isEarlyExit);
    }

    /**
     * Generate an absent attendance record
     */
    private function generateAbsentRecord(User $user, Carbon $date, DutySchedule $dutySchedule): array
    {
        return [
            'user_id'               => $user->id,
            'date'                  => $date->format('Y-m-d'),
            'check_in'              => null,
            'check_out'             => null,
            'stay_time'             => null,
            'worked_time'           => null,
            'break_time'            => '00:00:00',
            'over_time'             => '00:00:00',
            'late_duration'         => null,
            'early_exit_duration'   => null,
            'checkin_status'        => AttendanceStatus::ABSENT,
            'checkout_status'       => null,
            'late_reason'           => null,
            'early_exit_reason'     => null,
            'exempt_from_penalties' => false,
            'check_in_info'         => null,
            'check_out_info'        => null,
            'attendance_log'        => json_encode([
                'status' => 'absent',
                'reason' => 'No check-in recorded',
                'date'   => $date->format('Y-m-d'),
            ]),
            'duty_schedule_id'      => $dutySchedule->id,
            'status'                => 'check_out',
            'company_id'            => $user->company_id,
            'branch_id'             => $user->branch_id,
            'created_at'            => $date->format('Y-m-d H:i:s'),
            'updated_at'            => $date->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Generate a present attendance record
     */
    private function generatePresentRecord(User $user, Carbon $date, DutySchedule $dutySchedule, bool $isLate, bool $isEarlyExit): array
    {
        $scheduleStart = Carbon::createFromFormat('H:i:s', $dutySchedule->start_time);
        $scheduleEnd   = Carbon::createFromFormat('H:i:s', $dutySchedule->end_time);

        // Generate check-in time
        $checkInTime = $this->generateCheckInTime($date, $scheduleStart, $isLate);

        // Generate check-out time
        $checkOutTime = $this->generateCheckOutTime($date, $scheduleEnd, $isEarlyExit);

        // Calculate durations
        $stayTime   = $checkInTime->diffInSeconds($checkOutTime);
        $breakTime  = rand(30, 90) * 60; // 30-90 minutes break
        $workedTime = $stayTime - $breakTime;

        // Calculate late duration
        $lateDuration = null;
        $lateReason   = null;
        if ($isLate) {
            $lateDuration = $scheduleStart->diffInSeconds($checkInTime);
            $lateReason   = $this->getRandomLateReason();
        }

        // Calculate early exit duration
        $earlyExitDuration = null;
        $earlyExitReason   = null;
        if ($isEarlyExit) {
            $earlyExitDuration = $checkOutTime->diffInSeconds($scheduleEnd);
            $earlyExitReason   = $this->getRandomEarlyExitReason();
        }

        // Calculate overtime
        $overTime = 0;
        if (!$isEarlyExit && $checkOutTime->gt($scheduleEnd)) {
            $overTime = $scheduleEnd->diffInSeconds($checkOutTime);
        }

        return [
            'user_id'               => $user->id,
            'date'                  => $date->format('Y-m-d'),
            'check_in'              => $checkInTime->format('Y-m-d H:i:s'),
            'check_out'             => $checkOutTime->format('Y-m-d H:i:s'),
            'stay_time'             => $this->secondsToTime($stayTime),
            'worked_time'           => $this->secondsToTime($workedTime),
            'break_time'            => $this->secondsToTime($breakTime),
            'over_time'             => $this->secondsToTime($overTime),
            'late_duration'         => $lateDuration ? $this->secondsToTime($lateDuration) : null,
            'early_exit_duration'   => $earlyExitDuration ? $this->secondsToTime($earlyExitDuration) : null,
            'checkin_status'        => $isLate ? AttendanceStatus::LATE : AttendanceStatus::ON_TIME,
            'checkout_status'       => $isEarlyExit ? AttendanceStatus::LEFT_EARLY : ($overTime > 0 ? AttendanceStatus::LEFT_LATER : AttendanceStatus::LEFT_TIMELY),
            'late_reason'           => $lateReason,
            'early_exit_reason'     => $earlyExitReason,
            'exempt_from_penalties' => rand(1, 100) <= 5, // 5% chance of exemption
            'check_in_info'         => json_encode([
                'ip_address' => $this->generateRandomIP(),
                'location'   => $this->generateRandomLocation(),
                'device'     => $this->generateRandomDevice(),
                'method'     => 'normal_attendance',
            ]),
            'check_out_info'        => json_encode([
                'ip_address' => $this->generateRandomIP(),
                'location'   => $this->generateRandomLocation(),
                'device'     => $this->generateRandomDevice(),
                'method'     => 'normal_attendance',
            ]),
            'attendance_log'        => json_encode([
                'check_in'    => $checkInTime->format('H:i:s'),
                'check_out'   => $checkOutTime->format('H:i:s'),
                'status'      => $isLate ? 'late' : 'on_time',
                'break_taken' => $this->secondsToTime($breakTime),
            ]),
            'duty_schedule_id'      => $dutySchedule->id,
            'status'                => 'check_out',
            'company_id'            => $user->company_id,
            'branch_id'             => $user->branch_id,
            'created_at'            => $checkInTime->format('Y-m-d H:i:s'),
            'updated_at'            => $checkOutTime->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Generate check-in time
     */
    private function generateCheckInTime(Carbon $date, Carbon $scheduleStart, bool $isLate): Carbon
    {
        $checkInTime = $date->copy()->setTimeFrom($scheduleStart);

        if ($isLate) {
            // Late by 5-60 minutes
            $lateMinutes = rand(5, 60);
            $checkInTime->addMinutes($lateMinutes);
        } else {
            // Early by 0-15 minutes or exactly on time
            $earlyMinutes = rand(-15, 0);
            $checkInTime->addMinutes($earlyMinutes);
        }

        return $checkInTime;
    }

    /**
     * Generate check-out time
     */
    private function generateCheckOutTime(Carbon $date, Carbon $scheduleEnd, bool $isEarlyExit): Carbon
    {
        $checkOutTime = $date->copy()->setTimeFrom($scheduleEnd);

        if ($isEarlyExit) {
            // Leave 15-120 minutes early
            $earlyMinutes = rand(15, 120);
            $checkOutTime->subMinutes($earlyMinutes);
        } else {
            // Stay 0-60 minutes late or leave exactly on time
            $lateMinutes = rand(0, 60);
            $checkOutTime->addMinutes($lateMinutes);
        }

        return $checkOutTime;
    }

    /**
     * Convert seconds to HH:MM:SS format
     */
    private function secondsToTime(int $seconds): string
    {
        $hours   = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * Get random late reason
     */
    private function getRandomLateReason(): string
    {
        $reasons = [
            'Traffic jam',
            'Public transport delay',
            'Personal emergency',
            'Medical appointment',
            'Family emergency',
            'Car breakdown',
            'Weather conditions',
            'Overslept',
            'Child care issues',
            'Internet connectivity issues',
        ];

        return $reasons[array_rand($reasons)];
    }

    /**
     * Get random early exit reason
     */
    private function getRandomEarlyExitReason(): string
    {
        $reasons = [
            'Medical appointment',
            'Family emergency',
            'Personal appointment',
            'Feeling unwell',
            'Child pickup',
            'Pre-approved leave',
            'Emergency at home',
            'Transportation issues',
            'Power outage',
            'Internet issues',
        ];

        return $reasons[array_rand($reasons)];
    }

    /**
     * Generate random IP address
     */
    private function generateRandomIP(): string
    {
        return rand(192, 203) . '.' . rand(168, 255) . '.' . rand(1, 255) . '.' . rand(1, 254);
    }

    /**
     * Generate random location data
     */
    private function generateRandomLocation(): array
    {
        $locations = [
            ['lat' => 23.8103, 'lng' => 90.4125, 'address' => 'Dhaka, Bangladesh'],
            ['lat' => 23.7985, 'lng' => 90.3842, 'address' => 'Gulshan, Dhaka'],
            ['lat' => 23.7461, 'lng' => 90.3742, 'address' => 'Dhanmondi, Dhaka'],
            ['lat' => 23.8223, 'lng' => 90.3654, 'address' => 'Uttara, Dhaka'],
            ['lat' => 23.7808, 'lng' => 90.4217, 'address' => 'Motijheel, Dhaka'],
        ];

        return $locations[array_rand($locations)];
    }

    /**
     * Generate random device information
     */
    private function generateRandomDevice(): array
    {
        $devices = [
            ['type' => 'mobile', 'name' => 'iPhone 13', 'os' => 'iOS 15.6'],
            ['type' => 'mobile', 'name' => 'Samsung Galaxy S22', 'os' => 'Android 12'],
            ['type' => 'desktop', 'name' => 'Windows PC', 'os' => 'Windows 11'],
            ['type' => 'desktop', 'name' => 'MacBook Pro', 'os' => 'macOS Monterey'],
            ['type' => 'mobile', 'name' => 'Google Pixel 6', 'os' => 'Android 13'],
            ['type' => 'tablet', 'name' => 'iPad Air', 'os' => 'iPadOS 15.6'],
        ];

        return $devices[array_rand($devices)];
    }
}
