{"__meta": {"id": "01JYGWHNHMJ09X5DTGAFW3WD1T", "datetime": "2025-06-24 17:53:03", "utime": **********.284703, "method": "GET", "uri": "/hrm/attendance?page=1&from=&to=&short_by=&limit=10&search=&department=%20&user_id=&_token=m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU", "ip": "127.0.0.1"}, "php": {"version": "8.3.10", "interface": "cgi-fcgi"}, "messages": {"count": 10, "messages": [{"message": "[17:53:03] LOG.warning: htmlentities(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\XSS.php on line 37", "message_html": null, "is_string": false, "label": "warning", "time": **********.210322, "xdebug_link": null, "collector": "log"}, {"message": "[17:53:03] LOG.warning: htmlentities(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\XSS.php on line 37", "message_html": null, "is_string": false, "label": "warning", "time": **********.210455, "xdebug_link": null, "collector": "log"}, {"message": "[17:53:03] LOG.warning: htmlentities(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\XSS.php on line 37", "message_html": null, "is_string": false, "label": "warning", "time": **********.21055, "xdebug_link": null, "collector": "log"}, {"message": "[17:53:03] LOG.warning: htmlentities(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\XSS.php on line 37", "message_html": null, "is_string": false, "label": "warning", "time": **********.210638, "xdebug_link": null, "collector": "log"}, {"message": "[17:53:03] LOG.warning: htmlentities(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\XSS.php on line 37", "message_html": null, "is_string": false, "label": "warning", "time": **********.210706, "xdebug_link": null, "collector": "log"}, {"message": "[17:53:03] LOG.warning: htmlentities(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\XSS.php on line 37", "message_html": null, "is_string": false, "label": "warning", "time": **********.210767, "xdebug_link": null, "collector": "log"}, {"message": "[17:53:03] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Helpers\\CoreApp\\Traits\\TimeDurationTrait.php on line 52", "message_html": null, "is_string": false, "label": "warning", "time": **********.231806, "xdebug_link": null, "collector": "log"}, {"message": "[17:53:03] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Helpers\\CoreApp\\Traits\\TimeDurationTrait.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.23189, "xdebug_link": null, "collector": "log"}, {"message": "[17:53:03] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Helpers\\CoreApp\\Traits\\TimeDurationTrait.php on line 52", "message_html": null, "is_string": false, "label": "warning", "time": **********.237273, "xdebug_link": null, "collector": "log"}, {"message": "[17:53:03] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in C:\\laragon\\www\\onest-hrm\\app\\Helpers\\CoreApp\\Traits\\TimeDurationTrait.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.237357, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750765982.231676, "end": **********.284723, "duration": 1.0530469417572021, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": 1750765982.231676, "relative_start": 0, "end": **********.178166, "relative_end": **********.178166, "duration": 0.****************, "duration_str": "946ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.178174, "relative_start": 0.***************, "end": **********.284725, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.187365, "relative_start": 0.****************, "end": **********.193815, "relative_end": **********.193815, "duration": 0.*****************, "duration_str": "6.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.28101, "relative_start": 1.****************, "end": **********.283028, "relative_end": **********.283028, "duration": 0.002017974853515625, "duration_str": "2.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x backend.pagination.custom", "param_count": null, "params": [], "start": **********.28029, "type": "blade", "hash": "bladeC:\\laragon\\www\\onest-hrm\\resources\\views/backend/pagination/custom.blade.phpbackend.pagination.custom", "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fresources%2Fviews%2Fbackend%2Fpagination%2Fcustom.blade.php:1", "ajax": false, "filename": "custom.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.pagination.custom"}]}, "route": {"uri": "GET hrm/attendance", "middleware": "web, demo.mode, xss, admin, MaintenanceMode, FeatureCheck:attendance, PermissionCheck:attendance_read", "domain": "http://onest-hrm.test", "controller": "App\\Http\\Controllers\\Backend\\Attendance\\AttendanceController@index<a href=\"cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FHttp%2FControllers%2FBackend%2FAttendance%2FAttendanceController.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "attendance.index", "prefix": "hrm/attendance", "file": "<a href=\"cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FHttp%2FControllers%2FBackend%2FAttendance%2FAttendanceController.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/Attendance/AttendanceController.php:30-45</a>"}, "queries": {"count": 47, "nb_statements": 47, "nb_visible_statements": 47, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.017369999999999997, "accumulated_duration_str": "17.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select \"value\", \"key\" from \"company_configs\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Helpers\\Helper.php", "line": 447}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 21, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Helpers\\Helper.php", "line": 446}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": "middleware", "name": "demo.mode", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\DemoModeMiddleware.php", "line": 34}], "start": **********.206032, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Helper.php:447", "source": {"index": 17, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Helpers\\Helper.php", "line": 447}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FHelpers%2FHelper.php:447", "ajax": false, "filename": "Helper.php", "line": "447"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 0, "width_percent": 6.851}, {"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 21, "namespace": "middleware", "name": "admin", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\Admin.php", "line": 13}], "start": **********.211582, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 6.851, "width_percent": 8.405}, {"sql": "select * from \"user_breaks\" where \"user_id\" = 1 and \"end_time\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "admin", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\Admin.php", "line": 20}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 16, "namespace": "middleware", "name": "xss", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\XSS.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 18, "namespace": "middleware", "name": "demo.mode", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\DemoModeMiddleware.php", "line": 34}], "start": **********.214905, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "admin:20", "source": {"index": 14, "namespace": "middleware", "name": "admin", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\Admin.php", "line": 20}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FHttp%2FMiddleware%2FAdmin.php:20", "ajax": false, "filename": "Admin.php", "line": "20"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 15.256, "width_percent": 3.8}, {"sql": "select * from \"roles\" where \"roles\".\"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "PermissionCheck", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\PermissionCheck.php", "line": 31}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": "middleware", "name": "FeatureCheck", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\FeatureCheck.php", "line": 20}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "MaintenanceMode", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\SiteMaintenanceMiddleware.php", "line": 19}], "start": **********.217818, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "PermissionCheck:31", "source": {"index": 21, "namespace": "middleware", "name": "PermissionCheck", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Middleware\\PermissionCheck.php", "line": 31}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FHttp%2FMiddleware%2FPermissionCheck.php:31", "ajax": false, "filename": "PermissionCheck.php", "line": "31"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 19.056, "width_percent": 6.621}, {"sql": "select count(*) as aggregate from \"attendances\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 115}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.220016, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:115", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 115}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:115", "ajax": false, "filename": "AttendanceRepository.php", "line": "115"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 25.676, "width_percent": 5.124}, {"sql": "select * from \"attendances\" order by \"created_at\" desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 115}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.2219489, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:115", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 115}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:115", "ajax": false, "filename": "AttendanceRepository.php", "line": "115"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 30.8, "width_percent": 2.418}, {"sql": "select * from \"users\" where \"users\".\"id\" = 10 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [10, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2240498, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 33.218, "width_percent": 1.439}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.225531, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 34.657, "width_percent": 4.088}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 10 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [10, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2273872, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 38.745, "width_percent": 1.554}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 10 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 10, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.228424, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 40.299, "width_percent": 1.67}, {"sql": "select * from \"uploads\" where \"uploads\".\"id\" is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Helpers\\Helper.php", "line": 213}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 24, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Helpers\\Helper.php", "line": 212}, {"index": 26, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 128}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}], "start": **********.2299201, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Helper.php:213", "source": {"index": 20, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Helpers\\Helper.php", "line": 213}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FHelpers%2FHelper.php:213", "ajax": false, "filename": "Helper.php", "line": "213"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 41.969, "width_percent": 5.642}, {"sql": "select * from \"users\" where \"users\".\"id\" = 11 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [11, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2330601, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 47.611, "width_percent": 1.9}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.234124, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 49.511, "width_percent": 1.151}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 11 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [11, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.234993, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 50.662, "width_percent": 1.151}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 11 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 11, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.235893, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 51.813, "width_percent": 1.267}, {"sql": "select * from \"users\" where \"users\".\"id\" = 5 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [5, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.23797, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 53.08, "width_percent": 1.727}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.238967, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 54.807, "width_percent": 1.036}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 5 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [5, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.239789, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 55.843, "width_percent": 1.094}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 5 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 5, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.24065, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 56.937, "width_percent": 1.036}, {"sql": "select * from \"users\" where \"users\".\"id\" = 7 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [7, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2424319, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 57.974, "width_percent": 1.209}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2434158, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 59.182, "width_percent": 1.324}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 7 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [7, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.244374, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 60.507, "width_percent": 1.094}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 7 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 7, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.245281, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 61.6, "width_percent": 2.13}, {"sql": "select * from \"users\" where \"users\".\"id\" = 15 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [15, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2478411, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 63.731, "width_percent": 2.879}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.249187, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 66.609, "width_percent": 1.842}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 15 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [15, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.250365, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 68.451, "width_percent": 1.785}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 15 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 15, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.252031, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 70.236, "width_percent": 1.957}, {"sql": "select * from \"users\" where \"users\".\"id\" = 6 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [6, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.254257, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 72.193, "width_percent": 2.188}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2554379, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 74.381, "width_percent": 1.324}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 6 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [6, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2564108, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 75.705, "width_percent": 1.382}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 6 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 6, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.257398, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 77.087, "width_percent": 1.036}, {"sql": "select * from \"users\" where \"users\".\"id\" = 12 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [12, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.259187, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 78.123, "width_percent": 1.209}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.260018, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 79.332, "width_percent": 0.806}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 12 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [12, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2607691, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 80.138, "width_percent": 0.864}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 12 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 12, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.261589, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 81.002, "width_percent": 1.209}, {"sql": "select * from \"users\" where \"users\".\"id\" = 9 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [9, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.263497, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 82.211, "width_percent": 1.957}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2646842, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 84.168, "width_percent": 1.612}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 9 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [9, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.265811, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 85.78, "width_percent": 1.67}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 9 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 9, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.2669358, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 87.45, "width_percent": 1.267}, {"sql": "select * from \"users\" where \"users\".\"id\" = 16 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [16, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.268925, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 88.716, "width_percent": 1.957}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.270017, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 90.674, "width_percent": 1.094}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 16 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [16, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.270851, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 91.767, "width_percent": 1.094}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 16 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 16, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.2718139, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 92.861, "width_percent": 1.151}, {"sql": "select * from \"users\" where \"users\".\"id\" = 4 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [4, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.273732, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:119", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 119}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:119", "ajax": false, "filename": "AttendanceRepository.php", "line": "119"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 94.013, "width_percent": 2.36}, {"sql": "select * from \"departments\" where \"departments\".\"id\" = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 28, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.274916, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:124", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 124}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:124", "ajax": false, "filename": "AttendanceRepository.php", "line": "124"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 96.373, "width_percent": 1.382}, {"sql": "select count(*) as aggregate from \"user_breaks\" where \"user_id\" = 4 and \"date\" = '2025-06-23' and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": [4, "2025-06-23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.275864, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:125", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 125}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:125", "ajax": false, "filename": "AttendanceRepository.php", "line": "125"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 97.755, "width_percent": 1.036}, {"sql": "select * from \"user_breaks\" where \"date\" = '2025-06-23' and \"user_id\" = 4 and \"user_breaks\".\"company_id\" = 1 and \"user_breaks\".\"branch_id\" = 1", "type": "query", "params": [], "bindings": ["2025-06-23", 4, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, {"index": 16, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 126}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 118}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Backend/Attendance/AttendanceController.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Http\\Controllers\\Backend\\Attendance\\AttendanceController.php", "line": 33}], "start": **********.2767549, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "AttendanceRepository.php:835", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Hrm/Attendance/AttendanceRepository.php", "file": "C:\\laragon\\www\\onest-hrm\\app\\Repositories\\Hrm\\Attendance\\AttendanceRepository.php", "line": 835}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FRepositories%2FHrm%2FAttendance%2FAttendanceRepository.php:835", "ajax": false, "filename": "AttendanceRepository.php", "line": "835"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 98.791, "width_percent": 1.209}]}, "models": {"data": {"App\\Models\\User": {"value": 11, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Hrm\\Attendance\\Attendance": {"value": 10, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FModels%2FHrm%2FAttendance%2FAttendance.php:1", "ajax": false, "filename": "Attendance.php", "line": "?"}}, "App\\Models\\Hrm\\Department\\Department": {"value": 10, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FModels%2FHrm%2FDepartment%2FDepartment.php:1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\Role\\Role": {"value": 1, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FModels%2FRole%2FRole.php:1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 32, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"session_company_id": "1", "_token": "m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU", "url": "array:1 [\n  \"intended\" => \"http://onest-hrm.test/chatbot\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://onest-hrm.test/hrm/attendance\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "session_branch_id": "1", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "password_hash_web": "$2y$10$6QCozhwVP14YWarDth2to.85wrvBG4n4lolY2yyDWMNcu.R4VFzAC"}, "request": {"data": {"status": "200 OK", "full_url": "http://onest-hrm.test/hrm/attendance?_token=m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU&department=%20&...", "action_name": "attendance.index", "controller_action": "App\\Http\\Controllers\\Backend\\Attendance\\AttendanceController@index", "uri": "GET hrm/attendance", "domain": "http://onest-hrm.test", "controller": "App\\Http\\Controllers\\Backend\\Attendance\\AttendanceController@index<a href=\"cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FHttp%2FControllers%2FBackend%2FAttendance%2FAttendanceController.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "hrm/attendance", "file": "<a href=\"cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FHttp%2FControllers%2FBackend%2FAttendance%2FAttendanceController.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/Attendance/AttendanceController.php:30-45</a>", "middleware": "web, demo.mode, web, xss, admin, MaintenanceMode, FeatureCheck:attendance, PermissionCheck:attendance_read", "telescope": "<a href=\"http://onest-hrm.test/_debugbar/telescope/9f3b3273-b161-439f-9872-263728a432b1\" target=\"_blank\">View in Telescope</a>", "duration": "1.06s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1677316772 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>from</span>\" => \"\"\n  \"<span class=sf-dump-key>to</span>\" => \"\"\n  \"<span class=sf-dump-key>short_by</span>\" => \"\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => \"\"\n  \"<span class=sf-dump-key>department</span>\" => \"\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677316772\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-648016585 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-648016585\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6Ii9lTURUS3hFMkxyY0d2SHUrdHQ0TGc9PSIsInZhbHVlIjoieEhjWXhBZ29XTGhORTY2Y0JRcFpXWlYwdUUwRStBa1dCdC95ekRqT09JNmZ2bDNhSkFydnFjbnJpa2dHVW5CMnYxN04vOU4vSDJrZjJ2cThjUkFtNVpOV0tlWEdnWEZKa2F1QWZuRzRMT1lCMDB3ZzFFbFNFZFJ3c0RGTnN1bFkiLCJtYWMiOiI4NGZjM2M0YTYwMGRlYjBmNDlhMzY1MmE4M2NlMzZjOWUyOGIxOGVmZDdkYjcwYzdhNzY2OWYzYTJkYWNiYmFkIiwidGFnIjoiIn0%3D; idl_session=eyJpdiI6IlcybTJnclAzS2lXY2creGp6SGcyblE9PSIsInZhbHVlIjoiRVpsbDcvSnE0Q2c0cFlNN0R0N2JWb01GcXUrV2Zpa0oxYzlESDZTLy9KeWVLY3IvazdNT1J1VUkxazdQQVFIcWtxNlp4VExKQ2M0aWl0THRsWXozdVR4cm1RTGN5OWlKekR4bk9ZdzRodExjVnhqeXozVzg4MGFnbFVXR2VwTzIiLCJtYWMiOiJiOWY5NTMxOTY2NzYzYzM1YzMwMDA2NTUyNThhYmY3MTljYzFjZGU1MDBmMDUwODBiNmUxY2ZiNTUyOWY4YjdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://onest-hrm.test/hrm/attendance</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">onest-hrm.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2089177298 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU</span>\"\n  \"<span class=sf-dump-key>idl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DYqGMQxh1PyKl8EE4iSo1uy4iIOtOotZHjuAQr15</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089177298\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1491416984 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 24 Jun 2025 11:53:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491416984\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1691423579 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_company_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://onest-hrm.test/chatbot</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://onest-hrm.test/hrm/attendance</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>session_branch_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$6QCozhwVP14YWarDth2to.85wrvBG4n4lolY2yyDWMNcu.R4VFzAC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691423579\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://onest-hrm.test/hrm/attendance?_token=m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU&department=%20&...", "action_name": "attendance.index", "controller_action": "App\\Http\\Controllers\\Backend\\Attendance\\AttendanceController@index"}, "badge": null}}