<?php

namespace Tests\Feature;

use App\Models\AttendanceConfiguration;
use App\Models\Hrm\Attendance\Attendance;
use App\Models\Hrm\Attendance\DutySchedule;
use App\Models\Hrm\Attendance\UserDutySchedule;
use App\Models\User;
use Database\Seeders\Admin\AttendanceConfigurationSeeder;
use Database\Seeders\Admin\UserDutyScheduleSeeder;
use Database\Seeders\AttendanceSeeder;
use Database\Seeders\DutyScheduleSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AttendanceSeederTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create basic dependencies
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\Admin\StatusSeeder']);
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\Admin\CompanySeeder']);
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\BranchSeeder']);
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\Admin\RoleSeeder']);
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\DepartmentSeeder']);
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\DesignationSeeder']);
        $this->artisan('db:seed', ['--class' => 'Database\Seeders\Admin\UserSeeder']);
    }

    /** @test */
    public function duty_schedule_seeder_creates_schedules()
    {
        $this->artisan('db:seed', ['--class' => DutyScheduleSeeder::class]);

        $this->assertDatabaseCount('duty_schedules', 7);
        
        // Check specific schedules exist
        $this->assertDatabaseHas('duty_schedules', [
            'shift' => 'Morning Shift',
            'start_time' => '09:00:00',
            'end_time' => '17:00:00',
        ]);
        
        $this->assertDatabaseHas('duty_schedules', [
            'shift' => 'Night Shift',
            'start_time' => '23:00:00',
            'end_time' => '07:00:00',
            'end_on_same_date' => 0,
        ]);
    }

    /** @test */
    public function user_duty_schedule_seeder_assigns_schedules()
    {
        $this->artisan('db:seed', ['--class' => DutyScheduleSeeder::class]);
        $this->artisan('db:seed', ['--class' => UserDutyScheduleSeeder::class]);

        $userCount = User::count();
        $this->assertDatabaseCount('user_duty_schedules', $userCount);

        // Check that each user has a duty schedule assigned
        $users = User::all();
        foreach ($users as $user) {
            $this->assertDatabaseHas('user_duty_schedules', [
                'user_id' => $user->id,
            ]);
        }
    }

    /** @test */
    public function attendance_configuration_seeder_creates_configs()
    {
        $this->artisan('db:seed', ['--class' => AttendanceConfigurationSeeder::class]);

        $userCount = User::count();
        $this->assertDatabaseCount('attendance_configurations', $userCount);

        // Check that configurations are role-appropriate
        $admin = User::whereHas('role', function ($query) {
            $query->where('slug', 'admin');
        })->first();

        if ($admin) {
            $config = AttendanceConfiguration::where('user_id', $admin->id)->first();
            $this->assertTrue($config->is_free_ip);
            $this->assertTrue($config->is_free_location);
        }
    }

    /** @test */
    public function attendance_seeder_creates_realistic_data()
    {
        $this->artisan('db:seed', ['--class' => DutyScheduleSeeder::class]);
        $this->artisan('db:seed', ['--class' => UserDutyScheduleSeeder::class]);
        $this->artisan('db:seed', ['--class' => AttendanceConfigurationSeeder::class]);
        $this->artisan('db:seed', ['--class' => AttendanceSeeder::class]);

        // Should have attendance records
        $this->assertTrue(Attendance::count() > 0);

        // Check for variety in attendance statuses
        $this->assertTrue(Attendance::where('checkin_status', 'on_time')->exists());
        $this->assertTrue(Attendance::where('checkin_status', 'late')->exists());
        $this->assertTrue(Attendance::where('checkin_status', 'absent')->exists());

        // Check that attendance records have proper relationships
        $attendance = Attendance::with(['user', 'schedule'])->first();
        $this->assertNotNull($attendance->user);
        $this->assertNotNull($attendance->schedule);

        // Check that JSON fields are properly formatted
        $presentAttendance = Attendance::whereNotNull('check_in_info')->first();
        if ($presentAttendance) {
            $checkInInfo = $presentAttendance->check_in_info;
            $this->assertArrayHasKey('ip_address', $checkInInfo);
            $this->assertArrayHasKey('location', $checkInInfo);
            $this->assertArrayHasKey('device', $checkInInfo);
        }
    }

    /** @test */
    public function attendance_data_has_realistic_patterns()
    {
        $this->artisan('db:seed', ['--class' => DutyScheduleSeeder::class]);
        $this->artisan('db:seed', ['--class' => UserDutyScheduleSeeder::class]);
        $this->artisan('db:seed', ['--class' => AttendanceConfigurationSeeder::class]);
        $this->artisan('db:seed', ['--class' => AttendanceSeeder::class]);

        $totalAttendance = Attendance::count();
        $absentCount = Attendance::where('checkin_status', 'absent')->count();
        $lateCount = Attendance::where('checkin_status', 'late')->count();

        // Check absence rate is reasonable (should be around 10%)
        $absenceRate = ($absentCount / $totalAttendance) * 100;
        $this->assertTrue($absenceRate >= 5 && $absenceRate <= 15, "Absence rate should be 5-15%, got {$absenceRate}%");

        // Check late rate is reasonable (should be around 20%)
        $lateRate = ($lateCount / $totalAttendance) * 100;
        $this->assertTrue($lateRate >= 10 && $lateRate <= 30, "Late rate should be 10-30%, got {$lateRate}%");
    }

    /** @test */
    public function factories_create_valid_data()
    {
        // Test Attendance factory
        $attendance = Attendance::factory()->create();
        $this->assertDatabaseHas('attendances', ['id' => $attendance->id]);

        // Test late attendance factory
        $lateAttendance = Attendance::factory()->late()->create();
        $this->assertEquals('late', $lateAttendance->checkin_status);
        $this->assertNotNull($lateAttendance->late_reason);

        // Test absent attendance factory
        $absentAttendance = Attendance::factory()->absent()->create();
        $this->assertEquals('absent', $absentAttendance->checkin_status);
        $this->assertNull($absentAttendance->check_in);

        // Test DutySchedule factory
        $schedule = DutySchedule::factory()->morningShift()->create();
        $this->assertEquals('Morning Shift', $schedule->shift);

        // Test AttendanceConfiguration factory
        $config = AttendanceConfiguration::factory()->admin()->create();
        $this->assertTrue($config->is_free_ip);
        $this->assertTrue($config->is_free_location);
    }

    /** @test */
    public function seeded_data_maintains_relationships()
    {
        $this->artisan('db:seed', ['--class' => DutyScheduleSeeder::class]);
        $this->artisan('db:seed', ['--class' => UserDutyScheduleSeeder::class]);
        $this->artisan('db:seed', ['--class' => AttendanceConfigurationSeeder::class]);
        $this->artisan('db:seed', ['--class' => AttendanceSeeder::class]);

        // Check User -> AttendanceConfiguration relationship
        $user = User::with('attendanceConfig')->first();
        $this->assertNotNull($user->attendanceConfig);

        // Check User -> DutySchedules relationship
        $userWithSchedules = User::with('dutySchedules')->first();
        $this->assertTrue($userWithSchedules->dutySchedules->count() > 0);

        // Check User -> Attendances relationship
        $userWithAttendances = User::with('attendances')->first();
        $this->assertTrue($userWithAttendances->attendances->count() > 0);

        // Check Attendance -> User relationship
        $attendance = Attendance::with('user')->first();
        $this->assertNotNull($attendance->user);

        // Check Attendance -> DutySchedule relationship
        $attendanceWithSchedule = Attendance::with('schedule')->first();
        $this->assertNotNull($attendanceWithSchedule->schedule);
    }
}
