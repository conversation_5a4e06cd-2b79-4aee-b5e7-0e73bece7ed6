<?php

namespace Database\Factories;

use App\Enums\AttendanceStatus;
use App\Models\Hrm\Attendance\Attendance;
use App\Models\Hrm\Attendance\DutySchedule;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttendanceFactory extends Factory
{
    protected $model = Attendance::class;

    public function definition(): array
    {
        $user = User::factory()->create();
        $dutySchedule = DutySchedule::factory()->create();
        
        $date = $this->faker->dateTimeBetween('-30 days', 'now');
        $isLate = $this->faker->boolean(20); // 20% chance of being late
        $isEarlyExit = $this->faker->boolean(15); // 15% chance of early exit
        
        $scheduleStart = Carbon::createFromFormat('H:i:s', $dutySchedule->start_time);
        $scheduleEnd = Carbon::createFromFormat('H:i:s', $dutySchedule->end_time);
        
        // Generate check-in time
        $checkInTime = Carbon::instance($date)->setTimeFrom($scheduleStart);
        if ($isLate) {
            $checkInTime->addMinutes($this->faker->numberBetween(5, 60));
        } else {
            $checkInTime->addMinutes($this->faker->numberBetween(-15, 0));
        }
        
        // Generate check-out time
        $checkOutTime = Carbon::instance($date)->setTimeFrom($scheduleEnd);
        if ($isEarlyExit) {
            $checkOutTime->subMinutes($this->faker->numberBetween(15, 120));
        } else {
            $checkOutTime->addMinutes($this->faker->numberBetween(0, 60));
        }
        
        // Calculate durations
        $stayTime = $checkInTime->diffInSeconds($checkOutTime);
        $breakTime = $this->faker->numberBetween(30, 90) * 60; // 30-90 minutes
        $workedTime = $stayTime - $breakTime;
        
        // Calculate late duration
        $lateDuration = null;
        if ($isLate) {
            $lateDuration = $scheduleStart->diffInSeconds($checkInTime);
        }
        
        // Calculate early exit duration
        $earlyExitDuration = null;
        if ($isEarlyExit) {
            $earlyExitDuration = $checkOutTime->diffInSeconds($scheduleEnd);
        }
        
        // Calculate overtime
        $overTime = 0;
        if (!$isEarlyExit && $checkOutTime->gt($scheduleEnd)) {
            $overTime = $scheduleEnd->diffInSeconds($checkOutTime);
        }

        return [
            'user_id' => $user->id,
            'date' => $date->format('Y-m-d'),
            'check_in' => $checkInTime->format('Y-m-d H:i:s'),
            'check_out' => $checkOutTime->format('Y-m-d H:i:s'),
            'stay_time' => $this->secondsToTime($stayTime),
            'worked_time' => $this->secondsToTime($workedTime),
            'break_time' => $this->secondsToTime($breakTime),
            'over_time' => $this->secondsToTime($overTime),
            'late_duration' => $lateDuration ? $this->secondsToTime($lateDuration) : null,
            'early_exit_duration' => $earlyExitDuration ? $this->secondsToTime($earlyExitDuration) : null,
            'checkin_status' => $isLate ? AttendanceStatus::LATE : AttendanceStatus::ON_TIME,
            'checkout_status' => $isEarlyExit ? AttendanceStatus::LEFT_EARLY : ($overTime > 0 ? AttendanceStatus::LEFT_LATER : AttendanceStatus::LEFT_TIMELY),
            'late_reason' => $isLate ? $this->faker->randomElement([
                'Traffic jam', 'Public transport delay', 'Personal emergency', 'Medical appointment'
            ]) : null,
            'early_exit_reason' => $isEarlyExit ? $this->faker->randomElement([
                'Medical appointment', 'Family emergency', 'Personal appointment', 'Feeling unwell'
            ]) : null,
            'exempt_from_penalties' => $this->faker->boolean(5), // 5% chance
            'check_in_info' => [
                'ip_address' => $this->faker->ipv4,
                'location' => [
                    'lat' => $this->faker->latitude(23.7, 23.9),
                    'lng' => $this->faker->longitude(90.3, 90.5),
                    'address' => $this->faker->address
                ],
                'device' => [
                    'type' => $this->faker->randomElement(['mobile', 'desktop', 'tablet']),
                    'name' => $this->faker->randomElement(['iPhone 13', 'Samsung Galaxy S22', 'Windows PC']),
                    'os' => $this->faker->randomElement(['iOS 15.6', 'Android 12', 'Windows 11'])
                ],
                'method' => 'normal_attendance'
            ],
            'check_out_info' => [
                'ip_address' => $this->faker->ipv4,
                'location' => [
                    'lat' => $this->faker->latitude(23.7, 23.9),
                    'lng' => $this->faker->longitude(90.3, 90.5),
                    'address' => $this->faker->address
                ],
                'device' => [
                    'type' => $this->faker->randomElement(['mobile', 'desktop', 'tablet']),
                    'name' => $this->faker->randomElement(['iPhone 13', 'Samsung Galaxy S22', 'Windows PC']),
                    'os' => $this->faker->randomElement(['iOS 15.6', 'Android 12', 'Windows 11'])
                ],
                'method' => 'normal_attendance'
            ],
            'attendance_log' => [
                'check_in' => $checkInTime->format('H:i:s'),
                'check_out' => $checkOutTime->format('H:i:s'),
                'status' => $isLate ? 'late' : 'on_time',
                'break_taken' => $this->secondsToTime($breakTime)
            ],
            'duty_schedule_id' => $dutySchedule->id,
            'status' => 'check_out',
            'company_id' => $user->company_id,
            'branch_id' => $user->branch_id,
        ];
    }

    /**
     * Create an absent attendance record
     */
    public function absent(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'check_in' => null,
                'check_out' => null,
                'stay_time' => null,
                'worked_time' => null,
                'break_time' => '00:00:00',
                'over_time' => '00:00:00',
                'late_duration' => null,
                'early_exit_duration' => null,
                'checkin_status' => AttendanceStatus::ABSENT,
                'checkout_status' => null,
                'late_reason' => null,
                'early_exit_reason' => null,
                'check_in_info' => null,
                'check_out_info' => null,
                'attendance_log' => [
                    'status' => 'absent',
                    'reason' => 'No check-in recorded',
                    'date' => $attributes['date']
                ],
            ];
        });
    }

    /**
     * Create a late attendance record
     */
    public function late(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'checkin_status' => AttendanceStatus::LATE,
                'late_reason' => $this->faker->randomElement([
                    'Traffic jam', 'Public transport delay', 'Personal emergency', 'Medical appointment'
                ]),
            ];
        });
    }

    /**
     * Create an early exit attendance record
     */
    public function earlyExit(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'checkout_status' => AttendanceStatus::LEFT_EARLY,
                'early_exit_reason' => $this->faker->randomElement([
                    'Medical appointment', 'Family emergency', 'Personal appointment', 'Feeling unwell'
                ]),
            ];
        });
    }

    /**
     * Convert seconds to HH:MM:SS format
     */
    private function secondsToTime(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;
        
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }
}
