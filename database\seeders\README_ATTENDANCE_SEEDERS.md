# Attendance System Seeders Documentation

This document explains the comprehensive attendance system seeders that create meaningful and relational data for your HR management system.

## Overview

The attendance seeders create realistic data with proper relationships between:
- Users and their duty schedules
- Attendance configurations based on user roles
- Attendance records with realistic patterns
- Proper foreign key relationships

## Seeders Included

### 1. DutyScheduleSeeder
Creates various shift types with realistic timing:

- **Morning Shift**: 09:00-17:00 (8 hours)
- **Day Shift**: 10:00-18:00 (8 hours) 
- **Evening Shift**: 15:00-23:00 (8 hours)
- **Night Shift**: 23:00-07:00 (8 hours, spans two days)
- **Flexible Hours**: 08:00-16:00 (8 hours, 30min consideration time)
- **Part Time Morning**: 09:00-13:00 (4 hours)
- **Part Time Evening**: 14:00-18:00 (4 hours)

Each schedule includes:
- Consideration time (grace period for late arrivals)
- Maximum overtime hours
- Whether shift ends on same date

### 2. UserDutyScheduleSeeder
Assigns duty schedules to users based on their roles:

- **Super Admin**: Morning Shift, Flexible Hours
- **Admin**: Morning Shift, Day Shift, Flexible Hours
- **HR**: Morning Shift, Day Shift
- **Staff**: All shifts including part-time options

Part-time employees are automatically assigned part-time schedules.

### 3. AttendanceConfigurationSeeder
Creates attendance configurations based on user roles:

**Attendance Methods by Role:**
- Super Admin: Normal, Face Recognition, QR Code
- Admin: Normal, Face Recognition
- HR: Normal, Face Recognition
- Staff: Normal only

**IP/Location Restrictions:**
- Higher roles have more freedom
- Staff have 60% chance of free IP, 50% chance of free location
- HR have 80% chance of free IP, 70% chance of free location
- Admins have full freedom

**Weekend Configurations:**
- 80% Saturday-Sunday
- 15% Friday-Saturday
- 5% Sunday only

### 4. AttendanceSeeder
Generates 30 days of realistic attendance data:

**Attendance Patterns:**
- 10% absence rate
- 20% late arrival rate
- 15% early exit rate
- 5% penalty exemption rate

**Realistic Data Includes:**
- Check-in/out times based on duty schedules
- Late durations (5-60 minutes)
- Early exit durations (15-120 minutes)
- Break times (30-90 minutes)
- Overtime calculations
- Realistic reasons for lateness/early exits
- IP addresses, locations, device info
- Comprehensive attendance logs

## Factories Included

### 1. AttendanceFactory
Creates individual attendance records with:
- Realistic timing patterns
- Proper status calculations
- Device and location information
- States: `absent()`, `late()`, `earlyExit()`

### 2. DutyScheduleFactory
Creates duty schedules with:
- Predefined shift patterns
- States: `morningShift()`, `nightShift()`, `partTime()`, `flexible()`

### 3. AttendanceConfigurationFactory
Creates attendance configurations with:
- Role-based settings
- States: `admin()`, `staff()`, `restricted()`, `flexible()`, `remote()`, `partTime()`

## Usage

### Running All Seeders
```bash
php artisan db:seed --class=RegularSeeder
```

### Running Individual Seeders
```bash
# Create duty schedules first
php artisan db:seed --class=DutyScheduleSeeder

# Assign schedules to users
php artisan db:seed --class=UserDutyScheduleSeeder

# Create attendance configurations
php artisan db:seed --class=AttendanceConfigurationSeeder

# Generate attendance records
php artisan db:seed --class=AttendanceSeeder
```

### Using Factories in Tests
```php
// Create attendance with factory
$attendance = Attendance::factory()->create();

// Create late attendance
$lateAttendance = Attendance::factory()->late()->create();

// Create absent attendance
$absentAttendance = Attendance::factory()->absent()->create();

// Create duty schedule
$schedule = DutySchedule::factory()->morningShift()->create();

// Create attendance configuration
$config = AttendanceConfiguration::factory()->admin()->create();
```

## Data Relationships

The seeders maintain proper relationships:

1. **Users** → **AttendanceConfiguration** (1:1)
2. **Users** → **DutySchedule** (Many:Many via UserDutySchedule)
3. **Users** → **Attendance** (1:Many)
4. **DutySchedule** → **Attendance** (1:Many)
5. **Company/Branch** → All models (Foreign Keys)

## Realistic Data Features

### Attendance Patterns
- Consistent with real workplace scenarios
- Role-based attendance flexibility
- Seasonal and random variations
- Proper weekend handling

### Time Calculations
- Accurate stay time, worked time, break time
- Overtime calculations
- Late/early exit duration tracking
- Proper time formatting (HH:MM:SS)

### Audit Information
- IP addresses for check-in/out
- GPS coordinates (Dhaka area)
- Device information
- Attendance method tracking
- Comprehensive logging

## Customization

### Modifying Attendance Patterns
Edit `AttendanceSeeder::generateAttendanceRecord()` to adjust:
- Absence rates
- Late arrival rates
- Early exit rates
- Break time ranges

### Adding New Shift Types
Edit `DutyScheduleSeeder` to add new shifts:
```php
[
    'shift' => 'Custom Shift',
    'start_time' => '12:00:00',
    'end_time' => '20:00:00',
    'hour' => 8,
    'max_over_time' => 3,
]
```

### Role-Based Customization
Modify role mappings in:
- `UserDutyScheduleSeeder::getDutyScheduleForUser()`
- `AttendanceConfigurationSeeder::getAttendanceMethodsForRole()`

## Dependencies

Ensure these seeders run first:
1. StatusSeeder
2. CompanySeeder
3. BranchSeeder
4. RoleSeeder
5. DepartmentSeeder
6. DesignationSeeder
7. UserSeeder

## Notes

- All seeders include proper foreign key constraint handling
- Data is generated with realistic Bangladesh timezone (Asia/Dhaka)
- IP addresses and locations are generated for Dhaka area
- Seeders are idempotent (can be run multiple times safely)
- Performance optimized with batch inserts
