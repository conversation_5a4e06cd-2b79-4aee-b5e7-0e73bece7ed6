<?php

namespace App\Console\Commands;

use Database\Seeders\RegularSeeder;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use Symfony\Component\Console\Output\ConsoleOutput;

class RegularDBMigrate extends Command
{
    protected $signature = 'regular:migrate {--seed : Seed the database after migration}';

    protected $description = 'Run migrations for hrm regular';

    public function handle()
    {
        $confirmation = $this->confirm('Are you sure to run migration?');

        if ($confirmation) {
            if (true) {
                // Disable foreign key constraints
                $this->disableForeignKeyConstraints();

                Artisan::call('db:wipe', ['--force' => true, '-vvv' => true]);
                $this->customStyleText('Running migrations ...', '#0a0a0a', '#dffa7f');

                $migrationPaths = \tenantMigrationPaths();

                foreach ($migrationPaths as $path) {
                    $this->customStyleText('Migrating: '.$path.' ...', '#0a0a0a', '#effcb1');

                    try {
                        Artisan::call('migrate', ['--path' => $path, '--force' => true, '-vvv' => true]);
                        $this->info(Artisan::output());
                    } catch (\Exception $e) {
                        $this->customStyleText('An error occurred while migrating: '.$e->getMessage().' ...', '#0a0a0a', '#ff9191');
                    }
                }

                $this->customStyleText('Migrations has been completed.', '#0a0a0a', '#42f569');

                if ($this->option('seed')) {
                    $this->customStyleText('Database seeding ...', '#0a0a0a', '#dffa7f');
                    Artisan::call('db:seed', [
                        '--class' => RegularSeeder::class,
                        '--force' => true,
                        '-vvv' => true,
                    ]);
                    $this->info(Artisan::output());
                    $this->customStyleText('Database has been successfully seeded.', '#0a0a0a', '#42f569');
                }

                // Enable foreign key constraints
                $this->enableForeignKeyConstraints();
            } else {
                $this->customStyleText('Regular HRM is false. Skipping migrations.', '#0a0a0a', '#fcbbd7');
            }
        }

        return 0;
    }

    /**
     * Disable foreign key constraints using the Eloquent model approach
     */
    protected function disableForeignKeyConstraints()
    {
        // Using Eloquent's unguard method to disable mass assignment protection
        Model::unguard();

        // Using Schema to disable foreign key constraints
        Schema::disableForeignKeyConstraints();
    }

    /**
     * Enable foreign key constraints using the Eloquent model approach
     */
    protected function enableForeignKeyConstraints()
    {
        // Re-enable foreign key constraints
        Schema::enableForeignKeyConstraints();

        // Re-enable mass assignment protection
        Model::reguard();
    }

    public function customStyleText($text, $textColorHex, $bgColorHex)
    {
        $output = new ConsoleOutput;
        $style = new OutputFormatterStyle($textColorHex, $bgColorHex);
        $output->getFormatter()->setStyle('custom-style', $style);
        $output->writeln('<custom-style>'.$text.'</>');
        $output->getFormatter()->setStyle('custom-style', new OutputFormatterStyle);
    }
}