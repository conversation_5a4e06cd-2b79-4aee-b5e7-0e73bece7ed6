<?php

namespace Database\Seeders;

use App\Models\Hrm\Attendance\DutySchedule;
use Illuminate\Database\Seeder;

class DutyScheduleSeeder extends Seeder
{
    public function run()
    {
        $input      = \session()->get('input');
        $company_id = $input['company_id'] ?? 1;
        $branch_id  = $input['branch_id'] ?? 1;

        // Clear existing duty schedules
        DutySchedule::truncate();

        $shifts = [
            [
                'shift'            => 'Morning Shift',
                'start_time'       => '09:00:00',
                'end_time'         => '17:00:00',
                'consider_time'    => 15,
                'hour'             => 8,
                'max_over_time'    => 4,
                'end_on_same_date' => 1,
                'description'      => 'Standard morning shift for office workers',
            ],
            [
                'shift'            => 'Day Shift',
                'start_time'       => '10:00:00',
                'end_time'         => '18:00:00',
                'consider_time'    => 10,
                'hour'             => 8,
                'max_over_time'    => 3,
                'end_on_same_date' => 1,
                'description'      => 'Regular day shift with flexible timing',
            ],
            [
                'shift'            => 'Evening Shift',
                'start_time'       => '15:00:00',
                'end_time'         => '23:00:00',
                'consider_time'    => 20,
                'hour'             => 8,
                'max_over_time'    => 2,
                'end_on_same_date' => 1,
                'description'      => 'Evening shift for customer support',
            ],
            [
                'shift'            => 'Night Shift',
                'start_time'       => '23:00:00',
                'end_time'         => '07:00:00',
                'consider_time'    => 30,
                'hour'             => 8,
                'max_over_time'    => 2,
                'end_on_same_date' => 0,
                'description'      => 'Night shift spanning two days',
            ],
            [
                'shift'            => 'Flexible Hours',
                'start_time'       => '08:00:00',
                'end_time'         => '16:00:00',
                'consider_time'    => 30,
                'hour'             => 8,
                'max_over_time'    => 6,
                'end_on_same_date' => 1,
                'description'      => 'Flexible working hours for senior staff',
            ],
            [
                'shift'            => 'Part Time Morning',
                'start_time'       => '09:00:00',
                'end_time'         => '13:00:00',
                'consider_time'    => 10,
                'hour'             => 4,
                'max_over_time'    => 2,
                'end_on_same_date' => 1,
                'description'      => 'Part-time morning shift',
            ],
            [
                'shift'            => 'Part Time Evening',
                'start_time'       => '14:00:00',
                'end_time'         => '18:00:00',
                'consider_time'    => 10,
                'hour'             => 4,
                'max_over_time'    => 2,
                'end_on_same_date' => 1,
                'description'      => 'Part-time evening shift',
            ],
        ];

        foreach ($shifts as $shift) {
            DutySchedule::create([
                'company_id'       => $company_id,
                'branch_id'        => $branch_id,
                'shift'            => $shift['shift'],
                'start_time'       => $shift['start_time'],
                'end_time'         => $shift['end_time'],
                'consider_time'    => $shift['consider_time'],
                'hour'             => $shift['hour'],
                'max_over_time'    => $shift['max_over_time'],
                'end_on_same_date' => $shift['end_on_same_date'],
                'status_id'        => 1, // Active status
            ]);
        }

        $this->command->info('Created ' . count($shifts) . ' duty schedules.');
    }
}
